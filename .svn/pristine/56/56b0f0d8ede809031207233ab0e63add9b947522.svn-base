<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<script type="text/javascript">
	$(document).ready(function() {
		init();
	});

	function init() {
		initButton();
		initInput();
	}

	function initInput() {
		// Add input-related functionality if needed
	}

	function initButton() {
		// Add button-related functionality if needed
	}
</script>

<main>
	<!-- S:CONTENTS AREA -->
	<div class="contents-wrap">
		<div class="round-view">
			<!-- Promo Banner -->
			<div class="promo-banner">
			<% 
			String userAgent = request.getHeader("User-Agent");
			boolean isMobile = false;
			
			if(userAgent != null) {
				userAgent = userAgent.toLowerCase();
				if(userAgent.contains("mobi") || userAgent.contains("android") || userAgent.contains("iphone") ){
					isMobile = true;
				}
			}
			
			request.setAttribute("isMobile", isMobile);
			%>
			<c:choose> 
				<c:when test="${isMobile}">
					<img src="${pageContext.request.contextPath}/kid/upload/banner/${infoPromo.MOBILE_IMG_URL}" alt="Promo Image">
				</c:when>
				<c:otherwise>
					<img src="${pageContext.request.contextPath}/kid/upload/banner/${infoPromo.IMG_URL}" alt="Promo Image">
				</c:otherwise>
			</c:choose>
				
			</div>
			<div class="promo-rgst-info bg-title-gray promo-title-news">
				<h1>${infoPromo.TITL}</h1>
				<div class="rgst-info">
					<span>By ${infoPromo.RGST_ID}</span>
					<span class="divider">|</span>
					<em>${infoPromo.RGST_TIME}</em>
				</div>
			</div>
			<dl class="round-content">
				<dd>
					<p>
						${infoPromo.INFO_DESC}
					</p>
				</dd>
			</dl>
		</div>
		<!-- Navigation Buttons (UNCHANGED) -->
		<div class="list-navi">
			<c:set var="showPreviousButton" value="${empty infoPromoPrevious ? 'display:none' : ''}" />
			<c:set var="showNextButton" value="${empty infoPromoNext ? 'display:none' : ''}" />
			<div>
				<a href="${pageContext.request.contextPath}/info/promo/detailInfoPromoMain?id=${infoPromoPrevious.SEQ_NO}" class="btn-next" style="${showPreviousButton}">
					<span class="max-lines-detail"><c:out value="${infoPromoPrevious.TITL}" escapeXml="false"/></span>Previous
				</a>
			</div>
			<div>
				<a href="${pageContext.request.contextPath}/info/promo/detailInfoPromoMain?id=${infoPromoNext.SEQ_NO}" class="btn-prev" style="${showNextButton}">
					Next<span class="max-lines"><c:out value="${infoPromoNext.TITL}" escapeXml="false"/></span>
				</a>
			</div>

		</div>

		<!-- Back to List Button -->
		<div class="list-view-btn">
			<a href="${pageContext.request.contextPath}/info/promo/getInfoPromosMain" class="btn btn-blue-mid al-c">List view</a>
		</div>
	</div>
	<!-- E:CONTENTS AREA -->
</main>

<style>
	.promo-banner {
		padding: 3rem 3rem 1rem 3rem;
		background: var(--gray-lev7);
	}
	/* Promo Banner */
	.promo-banner img {
		width: 100%;
		border-radius: 2rem;
		min-height: 300px;

	}
	/* Terms & Conditions */
	.terms-conditions {
		margin-top: 30px;
	}

	.terms-conditions h2 {
		font-size: 18px;
		font-weight: bold;
	}

	.terms-conditions p {
		font-size: 14px;
		color: #333;
	}
	/* Container Styling */
	.rgst-info {
		display: flex;
		align-items: center;
		justify-content: end;
		font-size: 16px;
		color: #666;
		gap: 8px; /* Adds spacing between items */
		min-width: 40%;
	}

	/* Styling for Admin/User Name */
	.rgst-info span {
		font-weight: 500;
		color: #555;
	}

	/* Divider Styling */
	.rgst-info .divider {
		color: #aaa;
		font-weight: normal;
	}

	/* Styling for Date */
	.rgst-info em {
		font-style: normal;
		color: #555;
	}
	main ol, main ul {
		padding-left: 1.5rem !important;
	}
	main ol li {
		list-style: decimal;
	}

	main ul li {
		list-style: disc;
	}

	@media screen and (max-width: 1000px) {
		.round-view {
			width: 100%;
			margin: 0 0 4rem;
			padding: 0;
			border-radius: 0;
		}
		.promo-banner {
			padding: 0
		}
		.promo-banner img {
			border-radius: 0;
		}
		.promo-rgst-info {
			width: 100%;
			background: var(--gray-lev7);
			display: flex;
			justify-content: space-between;
			min-height: 0;
			padding: 1.5rem;

		}
		.promo-title-news h1 {
			margin-bottom: 0;
		}
		.round-content {
			padding: 1.5rem;
		}
		.round-content img {
			max-width: 100%;
			height: auto;
		}

	}
</style>
