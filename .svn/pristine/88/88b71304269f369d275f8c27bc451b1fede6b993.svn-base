package id.co.kiwoom.kwis.sample.controller;

import id.co.kiwoom.kwis.common.util.StringUtil;
import id.co.kiwoom.kwis.sample.service.SampleService;
import id.co.kiwoom.kwis.sample.vo.Tlo1050Q02InVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;



@Slf4j
@Controller
public class SampleController{
	
//	@Autowired
//	private SampleService sampleService;
//
//	@GetMapping(value={"/hello"})
//    public Object helloWorld(HttpServletRequest req, HttpServletResponse res) {
//        return req.getContextPath()+"/sp/HelloWorld";
//    }
//	@GetMapping(value={"/hello2"})
//    public Object helloWorld2(HttpServletRequest req, HttpServletResponse res) {
//        return req.getContextPath()+"/sp/HelloWorld2";
//    }
//	@GetMapping(value={"/hello3"})
//    public Object helloWorld3(HttpServletRequest req, HttpServletResponse res) {
//        return req.getContextPath()+"/sp/HelloWorld3";
//    }
//
//	@GetMapping(value={"/dbtest"})
//	@ResponseBody
//	public ResponseEntity<List<HashMap<String, Object>>> testDB(HttpServletRequest req, HttpServletResponse res) {
//		List<HashMap<String, Object>> resulMap = null;
//		try {
//			 resulMap = sampleService.getKbCust();
//		} catch (Exception e) {
//			log.error("dbtest error");
//		}
//		return ResponseEntity.status(HttpStatus.OK).body(resulMap);
//	}
//
//
//	@RequestMapping(value="/upjong", method=RequestMethod.GET)
//    public Object upjong(HttpServletRequest request, Model model)  throws Exception {	// server is not ready yet.
//        String skCode = "KJ_ASII";//"KU_COMPOSITE";
//        sampleService.getCategoryItem(skCode);
//
//        return "slogin";
//    }
//
//    @RequestMapping(value="/idxList", method=RequestMethod.GET)
//    public Object idxList(HttpServletRequest request, Model model)  throws Exception {	// server is not ready yet.
//        List<HashMap<String, String>> resulMap =  new ArrayList<HashMap<String, String>>();
//        resulMap = sampleService.getForeignIdxList();
//
//        return ResponseEntity.status(HttpStatus.OK).body(resulMap);
//    }
//
//
//    /**
//     * <code>searchCategoryListForHero</code>
//     * @Description : called From HTS
//     * <AUTHOR>
//     * @modify :
//     * @since 2011.03.16
//     * @version 1.0
//     */
//    @RequestMapping(value="/ctList", method=RequestMethod.GET)
//    public Object searchCategoryListForHero() throws Exception {	// server is not ready yet.
//        List<HashMap<String, String>> resultList = new LinkedList<HashMap<String, String>>();
//        try {
//            HashMap<String, Object> taoParams = new HashMap<String, Object>();
//            taoParams.put("trName", "INIDXETC");	// tr name
//            taoParams.put("gfid", "FID_2017");		// gridFid name
//            taoParams.put("id", "2017");			// gridFid number
//            taoParams.put("nrow", "38"); 			// output next data count
//            taoParams.put("RETURN_TYPE", "LIST");	// return type
//
//            HashMap<String, String> taoValues = new HashMap<String, String>();
//    //      taoValues.put("FID_9008", "1");	// input data (FID_Config.xml)
//            taoParams.put("values", taoValues);
//
//            String[] colNames = { "FID_10", "FID_11", "FID_12", "FID_13", "FID_20", "FID_22", "FID_25", "FID_302", "FID_9001" };	// output data (FID_Config.xml)
//            taoParams.put("colNames", colNames);
//
//            resultList = (List<HashMap<String, String>>)sampleService.getCategoryListForHero(taoParams);
//        } catch(Exception e){
//            log.error("HeroAction error : getCategoryListForHero");
//            throw e;
//        }
//        return ResponseEntity.status(HttpStatus.OK).body(resultList);
//    }
//
//
//    /**
//     * <code>getTopStockFreq</code>
//     * @Description : Top Stock Gain
//     * <AUTHOR>
//     * @modify :
//     * @since 2011.03.16
//     * @version 1.0
//     */
//    @RequestMapping(value="/gnList", method=RequestMethod.GET)
//    public Object searchTopStockFreq() throws Exception {	// server is not ready yet.
//        List<HashMap<String, String>> resultList = new LinkedList<HashMap<String, String>>();
//
//        try {
//        HashMap<String, Object> taoParams = new HashMap<String, Object>();
//        taoParams.put("trName", "INSTKETC");
//        taoParams.put("gfid", "FID_2006");
//        taoParams.put("id", "2006");
//        taoParams.put("nrow", "20");
//        taoParams.put("RETURN_TYPE", "LIST");
//
//        HashMap<String, String> taoValues = new HashMap<String, String>();
////      taoValues.put("FID_9020", "1");
//        taoValues.put("FID_9023", "3");
//        taoValues.put("FID_9600", "1");
//        taoValues.put("FID_9601", "1");
//        taoParams.put("values", taoValues);
//
//        String[] colNames = { "FID_10", "FID_13", "FID_14", "FID_16", "FID_17", "FID_18", "FID_703", "FID_302", "FID_301", "FID_401"};
//        taoParams.put("colNames", colNames);
//
//        resultList = (List<HashMap<String, String>>)sampleService.getCommonTaoList(taoParams);
//        } catch(Exception e){
//            log.error("HeroAction error : getCategoryListForHero");
//            throw e;
//        }
//        return ResponseEntity.status(HttpStatus.OK).body(resultList);
//    }
//
//    /**
//     * <code>getCategoryGraphByDay</code>
//     * <AUTHOR>
//     * @modify :
//     * @since 2011.03.16
//     * @version 1.0
//     */
//    @RequestMapping(value="/dgList", method=RequestMethod.GET)
//    public Object searchCategoryChartByDay() throws Exception {
//        List<HashMap<String, String>> resultList = new LinkedList<HashMap<String, String>>();
//        try {
//        HashMap<String, Object> taoParams = new HashMap<String, Object>();
//        taoParams.put("trName", "INIDXGRP");
//        taoParams.put("gfid", "FID_3008");
//        taoParams.put("id", "3008");
//        taoParams.put("nrow", "60");
//        taoParams.put("RETURN_TYPE", "LIST");
//
//        HashMap<String, String> taoValues = new HashMap<String, String>();
//        taoValues.put("FID_9001", StringUtil.null2str("", "KU_COMPOSITE"));
////      taoValues.put("FID_9004", "20110309");
//        taoParams.put("values", taoValues);
//
////      String[] colNames = { "FID_10", "FID_11", "FID_12", "FID_13", "FID_14", "FID_16", "FID_17", "FID_18", "FID_22" };
//        String[] colNames = { "FID_10", "FID_11", "FID_12", "FID_13", "FID_22", "FID_301" };
//        taoParams.put("colNames", colNames);
//
//            resultList = (List<HashMap<String, String>>)sampleService.getCommonTaoList(taoParams);
//        }catch(Exception e) {
//            log.error("CategoryDayChartController error : getCategoryChartByDay");
//            throw e;
//        }
//        return ResponseEntity.status(HttpStatus.OK).body(resultList);
//    }
//
//    /**
//     * <code>getStockDayChartDataList</code>
//     * <AUTHOR>
//     * @modify :
//     * @since 2011.03.16
//     * @version 1.0
//     */
//    @RequestMapping(value="/chartList", method=RequestMethod.GET)
//    public Object searchStockChartDataList() throws Exception {
//        List<HashMap<String, String>> resultList = new LinkedList<HashMap<String, String>>();
//
//        try {
//            HashMap<String, Object> taoParams = new HashMap<String, Object>();
//            taoParams.put("stockCode", "");
//            taoParams.put("timeType", "W");
//            taoParams.put("chartType", "");
//            taoParams.put("interval", "");
//            taoParams.put("startDate", "");
//            resultList = (List<HashMap<String, String>>)sampleService.getStockDataList(taoParams);
//
//        } catch(Exception e) {
//            log.error("StockDayChartController error : searchStockChartDataList");
//            throw e;
//        }
//
//        return ResponseEntity.status(HttpStatus.OK).body(resultList);
//    }
//
//    /**
//     * <code>portpolio</code>
//     * <AUTHOR>
//     * @modify :
//     * @since
//     * @version 1.0
//     */
//    @RequestMapping(value="/portpolio", method=RequestMethod.GET)
//    public Object setTdg6019U04(Tlo1050Q02InVo tlo1050Q02InVo) throws Exception {	// server is not ready yet.
//        HashMap<String, Object> resultMap = new HashMap<String, Object>();
//        resultMap = sampleService.getTlo1050Q02(tlo1050Q02InVo);
//
//        return ResponseEntity.status(HttpStatus.OK).body(resultMap);
//    }
//
//    /**
//	 * <code>lucy filter test</code>
//	 * @Description : lucy filter
//	 * <AUTHOR>
//	 * @modify :
//	 * @since
//	 * @version 1.0
//	 */
//	@RequestMapping(value="/lucyTest", method=RequestMethod.GET)
//	public String lucy() throws Exception {
//		return "sp/lucyTest";
//	}
//
//	@RequestMapping(value="/lucyFilterTest", method=RequestMethod.POST)
//	@ResponseBody
//	public Object lucyFilterTest(HttpServletRequest req, HttpServletResponse res) throws Exception {
//		HashMap<String, Object> resultMap = new HashMap<String, Object>();
//		String inputMsg = (String)req.getParameter("inputMsg");
//
//		resultMap.put("result", inputMsg);
//		return ResponseEntity.status(HttpStatus.OK).body(resultMap);
//	}
//
//	@RequestMapping(value="/chartTest", method=RequestMethod.GET)
//	public String chartTest() throws Exception {
//		return "sp/chartTest";
//	}
//
//	@RequestMapping(value="/stockSearchTest", method=RequestMethod.GET)
//	public String stockSearchTest() throws Exception {
//		return "sp/stockSearchTest";
//	}
//
//	@RequestMapping(value="/ckEditor4Test", method=RequestMethod.GET)
//	public String ckEditor4Test(HttpServletRequest req) throws Exception {
//		return "frame:" + req.getContextPath() + "/sp/ckEditor4Test";
//	}
//
//	@RequestMapping(value="/ckEditor5Test", method=RequestMethod.GET)
//	public String ckEditor5Test(HttpServletRequest req) throws Exception {
//		return "frame:" + req.getContextPath() + "/sp/ckEditor5Test";
//	}
}
