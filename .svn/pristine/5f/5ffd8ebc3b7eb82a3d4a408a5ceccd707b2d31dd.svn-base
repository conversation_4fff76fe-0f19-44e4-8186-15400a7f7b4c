package id.co.kiwoom.kwis.info.promo.controller;

import id.co.kiwoom.kwis.banner.service.BannerService;
import id.co.kiwoom.kwis.banner.vo.BannerInVo;
import id.co.kiwoom.kwis.banner.vo.BannerRankInVo;
import id.co.kiwoom.kwis.banner.vo.BannerSearchVo;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Optional;

@Controller
@RequestMapping("/info/promo")
public class InfoPromoController {

	private static final String SVC_GB = "E02";
	private final BannerService bannerService;

	public InfoPromoController(BannerService bannerService) {
		this.bannerService = bannerService;
	}

	/** ✅ Get Info Event Main */
	@GetMapping("/getInfoPromoMain")
	public String getMarketMain(HttpServletRequest req) {
		return "frame:" + req.getContextPath() + "/info/promo/getInfoPromoMain";
	}

	@PostMapping(value = { "/getInfoPromoList" })
	@ResponseBody
	public Object getMarketList(BannerSearchVo params) throws Exception {
		return ResponseEntity.status(HttpStatus.OK).body(bannerService.getBannerList(params));
	}

	/** ✅ Register Info Event Main */
	@GetMapping("/regInfoPromoMain")
	public String regBanner(HttpServletRequest req) {
		return "frame:" + req.getContextPath() + "/info/promo/regInfoPromoMain";
	}

	/** ✅ Get Info Event Detail */
	@GetMapping("/detailInfoPromoMain")
	public String detailBanner(HttpServletRequest req, Model model, @RequestParam("id") int seqNo) {
		model.addAllAttributes(getBannerDetails(seqNo));
		return "frame:" + req.getContextPath() + "/info/promo/detailInfoPromoMain";
	}

	/** ✅ Update Info Event Main */
	@GetMapping("/updInfoPromoMain")
	public String updBanner(HttpServletRequest req, Model model, @RequestParam("id") int seqNo) {
		HashMap<String, Object> banners = getBannerDetails(seqNo);
		model.addAllAttributes(banners);

		int exposedCount = bannerService.countExposedBannerBySvcGb(SVC_GB, seqNo);
		model.addAttribute("exposedCount", exposedCount);

		return "frame:" + req.getContextPath() + "/info/promo/updInfoPromoMain";
	}


	@PostMapping(path = "/regInfoPromoMain")
	@ResponseBody
	public Object regBanner(BannerInVo bannerInVo) throws Exception {
		bannerInVo.setSvcGb(SVC_GB);
		return this.bannerService.insBanner(bannerInVo);
	}

	@PostMapping(path = "/updInfoPromoMain", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	@ResponseBody
	public Object updBanner(@ModelAttribute BannerInVo bannerInVo) throws Exception {
		bannerInVo.setSvcGb(SVC_GB);
		return this.bannerService.updBanner(bannerInVo);
	}
	@PostMapping(path = "/delInfoPromo")
	@ResponseBody
	public Object delBanner(@RequestBody BannerInVo bannerInVo) throws Exception {
		bannerInVo.setSvcGb(SVC_GB);
		bannerInVo.setInfoDesc(decodeText(bannerInVo.getInfoDesc()));
		bannerInVo.setTitle(decodeText(bannerInVo.getTitle()));
		return this.bannerService.delBanner(bannerInVo);
	}

	@PostMapping(path = "/updInfoPromoRank")
	@ResponseBody
	public Object updBannerRank(@RequestBody BannerRankInVo bannerRankInVo) throws Exception {
		this.bannerService.updBannerRank(bannerRankInVo);
		return true;
	}

	// ──────────────────────────────────────────────────────────────────────────────
	// 🔹 HELPER METHODS
	// ──────────────────────────────────────────────────────────────────────────────

	/**
	 * Retrieves banner details, decodes INFO_DESC, and returns a HashMap.
	 */
	private HashMap<String, Object> getBannerDetails(int seqNo) {
		HashMap<String, Object> banners = bannerService.selBannerDetail(seqNo, SVC_GB);
		if (banners != null) {
			HashMap<String, Object> banner = (HashMap<String, Object>) banners.get("banner");
			if (banner != null) {
				banner.put("INFO_DESC", decodeText((String) banner.get("INFO_DESC")));
				banner.put("TITL", decodeText((String) banner.get("TITL")));
			}
			HashMap<String, Object> preBanner = (HashMap<String, Object>) banners.get("bannerPrevious");
			if (preBanner != null) {
				preBanner.put("INFO_DESC", decodeText((String) preBanner.get("INFO_DESC")));
				preBanner.put("TITL", decodeText((String) preBanner.get("TITL")));
			}
			HashMap<String, Object> nextBanner = (HashMap<String, Object>) banners.get("bannerNext");
			if (nextBanner != null) {
				nextBanner.put("INFO_DESC", decodeText((String) nextBanner.get("INFO_DESC")));
				nextBanner.put("TITL", decodeText((String) nextBanner.get("TITL")));
			}
		}
		return Optional.ofNullable(banners).orElse(new HashMap<>());
	}

	/**
	 * Safely decodes URL-encoded text, skipping invalid % patterns.
	 */
	private String decodeText(String text) {
		return Optional.ofNullable(text)
				.map(t -> {
					try {
						// Sanitize: Remove any invalid % patterns (e.g. stray %, %Z, %1)
						t = sanitizeInvalidPercentEncoding(t);
						return URLDecoder.decode(t, StandardCharsets.UTF_8.name());
					} catch (Exception e) {
						e.printStackTrace(); // Log the error for debugging
						return t;
					}
				})
				.orElse("");
	}

	/**
	 * Sanitizes the string by removing invalid percent-encodings.
	 */
	private String sanitizeInvalidPercentEncoding(String input) {
		StringBuilder sanitized = new StringBuilder();
		int len = input.length();
		for (int i = 0; i < len; i++) {
			char c = input.charAt(i);
			if (c == '%' && i + 2 < len) {
				char c1 = input.charAt(i + 1);
				char c2 = input.charAt(i + 2);
				if (isHexChar(c1) && isHexChar(c2)) {
					sanitized.append('%').append(c1).append(c2);
					i += 2;
				} else {
					// Skip the invalid % sequence
					sanitized.append("%25"); // encode '%' as safe fallback
				}
			} else if (c == '%' && (i + 1 >= len || i + 2 >= len)) {
				// Incomplete trailing %
				sanitized.append("%25");
			} else {
				sanitized.append(c);
			}
		}
		return sanitized.toString();
	}

	/**
	 * Helper method to check if a character is a valid hex digit.
	 */
	private boolean isHexChar(char ch) {
		return (ch >= '0' && ch <= '9') ||
				(ch >= 'A' && ch <= 'F') ||
				(ch >= 'a' && ch <= 'f');
	}
}
