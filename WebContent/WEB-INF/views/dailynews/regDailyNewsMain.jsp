<script type="text/javascript" src="${pageContext.request.contextPath}/kid/js/libs/ckeditor4/ckeditor.js"></script>
<script type="text/javascript">
	let modalConfirm;
	let currentStockId = "";
	let currentStockName = "";
	let currentStockContainer = "";
	$(document).ready(function() {
		init();
	});

	function init() {
		initCkEditor();
		initModal();
		initValidate();
		initButton();
		initInput();
	}
	function initCkEditor() {
		var editor1 = CKEDITOR.replace('stockCont1');
		var editor2 = CKEDITOR.replace('stockCont2');
		var editor3 = CKEDITOR.replace('stockCont3');
		var editor4 = CKEDITOR.replace('stockCont4');
		var editor5 = CKEDITOR.replace('stockCont5');
	}
	function initModal() {
		modalConfirm = new bootstrap.Modal(document
				.getElementById('modalConfirm'), {
			backdrop : true
		});
	}
	function initInput() {

	}
	function initButton() {
		$("#btnSave").on('click', function() {
			if ($("#formDailyNews").valid()) {
				modalConfirm.toggle();
			}
		});
		$("#btnConfirm").on('click', function() {
			onSave();
		});
		$(".stockPopup").on('click', function() {
			currentStockId = $(this).attr("data-id");
			currentStockName = $(this).attr("data-name");
			currentStockContainer = $(this).attr("data-img-container");
			openStockSearchPop();
		});

	}
	function openStockSearchPop() {
		var url = "/common/getStockSearchPop";
		var style = "width=800,height=650,scrollbars=yes,resizable=noht";
		window.open(url, 'getStockSearchPop', style);
	}
	function initValidate() {
		$("#formDailyNews").validate({
			ignore: [],
			rules : {
				title : {
					required: true,
					maxlength: 50
				},
				stockCont1: {
					required: function(textarea) {
						CKEDITOR.instances[textarea.id].updateElement();
						var editorcontent = textarea.value.replace(/<[^>]*>/gi, '');
						return editorcontent.length == 0;
					}
				},
				file : {
					extension : "pdf"
				}
			},
			messages : {
				title : {
					required: "Please enter subject"
				},
				file : {
					extension : "File type is not allowed"
				}
			}
		});

	}
	function onSave() {
		if ($("#formDailyNews").valid()) {
			const params = {
				title : $('#txtSubject').val(),
				userId : $('#userId').val(),
				username : $('#username').val(),
				stockCode1 : $("#stockCode1").val(),
				stockNm1 : $("#stockNm1").val(),
				stockCont1 : CKEDITOR.instances.stockCont1.getData(),
				stockCode2 : $("#stockCode2").val(),
				stockNm2 : $("#stockNm2").val(),
				stockCont2 : CKEDITOR.instances.stockCont2.getData(),
				stockCode3 : $("#stockCode3").val(),
				stockNm3 : $("#stockNm3").val(),
				stockCont3 : CKEDITOR.instances.stockCont3.getData(),
				stockCode4 : $("#stockCode4").val(),
				stockNm4 : $("#stockNm4").val(),
				stockCont4 : CKEDITOR.instances.stockCont4.getData(),
				stockCode5 : $("#stockCode5").val(),
				stockNm5 : $("#stockNm5").val(),
				stockCont5 : CKEDITOR.instances.stockCont5.getData()
			}
			
			var jsonObj = {
				isShowLoading : true,
				method : 'POST',
				url : "/dailynews/regDailyNews",
				contentType : 'application/json; charset=utf-8',
				cache : false,
				data : JSON.stringify(params),
				async : true,
				success : function(res) {
					alertMessage.show('#success-alert', 3000);
					$('#formDailyNews input').val('');
					$('#formDailyNews textarea').val('');
					$('.imgContainer').empty().append('<img class="img-fluid mx-auto d-block" src="/kid/img/admin/no_image_icon.png">');
					CKEDITOR.instances.stockCont1.setData("");
					CKEDITOR.instances.stockCont2.setData("");
					CKEDITOR.instances.stockCont3.setData("");
					CKEDITOR.instances.stockCont4.setData("");
					CKEDITOR.instances.stockCont5.setData("");
				},
				error : function(e1, e2, e3) {
					alertMessage.show('#error-alert', 3000);
				}
			}
			kidCommon.ajax(jsonObj);
		}
	}
	function callBackStockInfo(stockCode, stockNm) {
		if (stockCode) {
			$('#' + currentStockId).val(stockCode);
			getStockDetail(stockCode);
		}
		if (stockNm) {
			$('#' + currentStockName).val(stockNm);
		}
	}
	function getStockDetail(stockCode) {
		const params = {
			stockCode : stockCode
		}
		var jsonObj = {
			isShowLoading : true,
			method : 'POST',
			url : "/stockimg/getStockImg",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(res) {
				let imgContainer = $('#' + currentStockContainer);
				let imgUrl = '${pageContext.request.contextPath}/kid/img/admin/no_image_icon.png'; 
				if (res.LOGO_IMG_FILE_NM !== undefined) {
					imgUrl = '${pageContext.request.contextPath}/kid/upload/stockimg/' + res.LOGO_IMG_FILE_NM;
				}
				var $img = $('<img class="img-fluid mx-auto d-block"/>').attr({
					src : imgUrl
				});
				$img.css({
					width : `${imageWidth}px`,
					height : `${imageHeight}px`,
				});
				imgContainer.empty().append($img).show();

			},
			error : function(e1, e2, e3) {
				alertMessage.show('#error-alert', 3000);
			}
		}
		kidCommon.ajax(jsonObj);
	}
	function onDelete() {
		const params = {
			id : $('#id').val()
		}
		var jsonObj = {
			isShowLoading : true,
			method : 'POST',
			url : "/dailynews/delDailyNews",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(res) {
				alertMessage.show('#success-alert', 3000);
				window.location = '/dailynews/getDailyNewsMain';
			},
			error : function(e1, e2, e3) {
				alertMessage.show('#error-alert', 3000);
			}
		}
		kidCommon.ajax(jsonObj);
	}
</script>
<div class="container-fluid p-0">
	<input type="hidden" value="#menuDailyNews" id="screenName" />
	<h1 class="h3 mb-3">
		<strong>Daily News</strong>
	</h1>
	<div class="row">
		<div class="col-12">
			<div class="card">
				<div class="card-body">
					<form id="formDailyNews">
						<div class="row">
							<div class="col-12 col-lg-12">
								<label for="txtSubject" class="fw-bold">
									Subject<span class="text-danger">*</span>
								</label>
								<input type="text" class="form-control" name="title" id="txtSubject" placeholder="" value="">
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-10 col-lg-10">
								<div class="row mt-2">
									<label for="txtSubject" class="fw-bold"> Stock Code 1 </label>
									<div class="input-group mb-3">
										<input type="text" class="form-control stockPopup" data-id="stockCode1" data-name="stockNm1" data-img-container="imgContainer1" id="stockCode1" readonly>
										<button class="btn btn-outline-secondary stockPopup" data-id="stockCode1" data-name="stockNm1" data-img-container="imgContainer1" type="button" id="btnSearch1">Search</button>
									</div>
								</div>
								<div class="row">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Name 1 </label>
										<input type="text" class="form-control stockPopup" data-id="stockCode1" data-name="stockNm1" id="stockNm1" placeholder="" readonly>
									</div>
								</div>
							</div>
							<div class="col-2 col-lg-2">
								<div class="row mt-2">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Logo 1 </label>
										<div id="imgContainer1" class="imgContainer border">
											<img class="img-fluid mx-auto d-block" src="${pageContext.request.contextPath}/kid/img/admin/no_image_icon.png">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="stockCont1" class="fw-bold">Stock Content 1<span class="text-danger">*</span></label>
								<textarea class="form-control " style="height: 150px" name="stockCont1" id="stockCont1" placeholder=""></textarea>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-10 col-lg-10">
								<div class="row mt-2">
									<label for="txtSubject" class="fw-bold"> Stock Code 2 </label>
									<div class="input-group mb-3">
										<input type="text" class="form-control stockPopup" data-id="stockCode2" data-name="stockNm2" data-img-container="imgContainer2" id="stockCode2" readonly>
										<button class="btn btn-outline-secondary stockPopup" data-id="stockCode2" data-name="stockNm2" data-img-container="imgContainer2" type="button" id="btnSearch2">Search</button>
									</div>
								</div>
								<div class="row">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Name 2 </label>
										<input type="text" class="form-control stockPopup" data-id="stockCode2" data-name="stockNm2" data-img-container="imgContainer2" id="stockNm2" placeholder="" readonly>
									</div>
								</div>
							</div>
							<div class="col-2 col-lg-2">
								<div class="row mt-2">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Logo 2 </label>
										<div id="imgContainer2" class="imgContainer border">
											<img class="img-fluid mx-auto d-block" src="${pageContext.request.contextPath}/kid/img/admin/no_image_icon.png">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="stockCont2" class="fw-bold">Stock Content 2 </label>
								<textarea class="form-control " style="height: 150px" name="stockContent2" id="stockCont2" placeholder=""></textarea>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-10 col-lg-10">
								<div class="row mt-2">
									<label for="txtSubject" class="fw-bold"> Stock Code 3 </label>
									<div class="input-group mb-3">
										<input type="text" class="form-control stockPopup" data-id="stockCode3" data-name="stockNm3" data-img-container="imgContainer3" id="stockCode3" readonly>
										<button class="btn btn-outline-secondary stockCode" data-id="stockCode3" data-name="stockNm3" data-img-container="imgContainer3" type="button" id="btnSearch3">Search</button>
									</div>
								</div>
								<div class="row">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Name 3 </label>
										<input type="text" class="form-control stockPopup" data-id="stockCode3" data-name="stockNm3" id="stockNm3" data-img-container="imgContainer3" placeholder="" readonly>
									</div>
								</div>
							</div>
							<div class="col-2 col-lg-2">
								<div class="row mt-2">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Logo 3 </label>
										<div id="imgContainer3" class="imgContainer border">
											<img class="img-fluid mx-auto d-block" src="${pageContext.request.contextPath}/kid/img/admin/no_image_icon.png">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="stockCont3" class="fw-bold">Stock Content 3 </label>
								<textarea class="form-control " style="height: 150px" name="stockContent3" id="stockCont3" placeholder=""></textarea>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-10 col-lg-10">
								<div class="row mt-2">
									<label for="txtSubject" class="fw-bold"> Stock Code 4 </label>
									<div class="input-group mb-3">
										<input type="text" class="form-control stockPopup" data-id="stockCode4" data-name="stockNm4" data-img-container="imgContainer4" id="stockCode4" readonly>
										<button class="btn btn-outline-secondary stockPopup" data-id="stockCode4" data-name="stockNm4" data-img-container="imgContainer4" type="button" id="btnSearch4">Search</button>
									</div>
								</div>
								<div class="row">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Name 4 </label>
										<input type="text" class="form-control stockPopup" data-id="stockCode4" data-name="stockNm4" data-img-container="imgContainer4" id="stockNm4" placeholder="" readonly>
									</div>
								</div>
							</div>
							<div class="col-2 col-lg-2">
								<div class="row mt-2">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Logo 4 </label>
										<div id="imgContainer4" class="imgContainer border">
											<img class="img-fluid mx-auto d-block" src="${pageContext.request.contextPath}/kid/img/admin/no_image_icon.png">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="stockCont4" class="fw-bold">Stock Content 4 </label>
								<textarea class="form-control " style="height: 150px" name="stockContent4" id="stockCont4" placeholder=""></textarea>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-10 col-lg-10">
								<div class="row mt-2">
									<label for="txtSubject" class="fw-bold"> Stock Code 5 </label>
									<div class="input-group mb-3">
										<input type="text" class="form-control stockPopup" data-id="stockCode5" data-name="stockNm5" data-img-container="imgContainer5" id="stockCode5" readonly>
										<button class="btn btn-outline-secondary stockPopup" data-id="stockCode5" data-name="stockNm5" data-img-container="imgContainer5" type="button" id="btnSearch5">Search</button>
									</div>
								</div>
								<div class="row">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Name 5 </label>
										<input type="text" class="form-control stockPopup" data-id="stockCode5" data-name="stockNm5" data-img-container="imgContainer5" id="stockNm5" placeholder="" readonly>
									</div>
								</div>
							</div>
							<div class="col-2 col-lg-2">
								<div class="row mt-2">
									<div class="col-12 col-lg-12">
										<label for="txtSubject" class="fw-bold"> Stock Logo 5 </label>
										<div id="imgContainer5" class="imgContainer border">
											<img class="img-fluid mx-auto d-block" src="${pageContext.request.contextPath}/kid/img/admin/no_image_icon.png">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="stockCont5" class="fw-bold">Stock Content 5 </label>
								<textarea class="form-control " style="height: 150px" name="stockContent5" id="stockCont5" placeholder=""></textarea>
							</div>
						</div>
						<div class="row mt-3">
							<div class="d-grid gap-2 d-md-flex justify-content-md-end">
								<a class="btn btn-light" href="/dailynews/getDailyNewsMain">List</a>
								<a class="btn btn-primary " id="btnSave">Save</a>
							</div>
						</div>
					</form>
				</div>
			</div>
			<div class="alert alert-success" id="success-alert" style="display: none;">
				<strong>Saved successfully.</strong>
			</div>
			<div class="alert alert-danger" role="alert" id="error-alert" style="display: none;">An error has occurred. Please contact your system administrator.</div>
		</div>
	</div>
</div>
<!-- Modal -->
<div class="modal fade" id="modalConfirm" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header bg-primary bg-gradient">
				<h5 class="modal-title fw-bold text-light">Confirmation</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">Do you want to register as it is?</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="btnConfirm">Confirm</button>
			</div>
		</div>
	</div>
</div>
