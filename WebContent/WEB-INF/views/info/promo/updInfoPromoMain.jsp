<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/kid/js/libs/ckeditor4/ckeditor.js"></script>
<script type="text/javascript">
	let validator;
	let imageWidth = 996;
	let imageHeight = 360;
	let mobileImageWidth = 280;
	let mobileImageHeight = 240;
	let modalDelete;
	let modalConfirm;
	$(document).ready(function() {
		init();
	});

	function init() {
		initCkEditor();
		initModal();
		initValidate();
		initButton();
		initInput();
		initData();
	}
	function initCkEditor() {
		var editor1 = CKEDITOR.replace('txtImgDesc');
	}
	function initInput() {

	}
	function initButton() {
		$("#btnSave").on('click', function() {
			if ($("#formInfoPromoMain").valid()) {
				modalConfirm.toggle();
			}
		});
		$("#btnConfirm").on('click', function() {
			onSave();
		});
		onSelectFileChange("#filePcImage", "#filePcImageContainer",
				"filePcImage",imageWidth,imageHeight);
		onSelectFileChange("#fileMobileImage", "#fileMobileImageContainer",
				"fileMobileImage",mobileImageWidth,mobileImageHeight);

		$("#btnDelete").on('click', function() {
			modalDelete.toggle();

		});
		$("#btnDelConfirm").on('click', function() {
			onDelete();
		});

	}
	function initValidate() {
		validator = $("#formInfoPromoMain").validate({
			ignore : [],
			rules : {
				title: {
					required: true,
					maxWords: 20
				},
				filePcImage: {
					required: true,
					extension: "png",
					imageWidth: imageWidth,
					imageHeight: imageHeight
				},
				fileMobileImage: {
					required: true,
					extension: "png",
					imageWidth: mobileImageWidth,
					imageHeight: mobileImageHeight
				},
				txtImgDesc: {
					ckeditorRequired: true,   // Ensure CKEditor content is required
					ckeditorMaxLength: 10000    // Ensure CKEditor content is max 10000 characters
				}
			},
			messages : {
				title: {
					maxWords: "Title must be 20 words or fewer."
				},
				filePcImage: {
					extension: "File type is not allowed"
				},
				fileMobileImage: {
					extension: "File type is not allowed"
				},
				txtImgDesc: {
					ckeditorRequired: "Field is required.",
					ckeditorMaxLength: "Description must be 10000 characters or less."
				}
			}
		});

	}
	function initModal() {
		modalDelete = new bootstrap.Modal(document
				.getElementById('modalDelete'), {
			backdrop : true
		});
		modalConfirm = new bootstrap.Modal(document
				.getElementById('modalConfirm'), {
			backdrop : true
		});
	}
	// Function to get and validate the link URL
	function getValidatedLink(inputSelector, selectSelector) {
		let link = $(inputSelector).val()?.trim() || ''; // Get input value and trim spaces

		// If the link does NOT start with "http://" or "https://", prepend selected option
		if (link !== '' && !/^https?:\/\//.test(link)) {
			link = $(selectSelector + ' option:selected').val() + link;
		}
		return link;
	}
	function onSave() {
		if ($("#formInfoPromoMain").valid()) {
			// Upload files first, then update the banner data
			uploadFilesAndUpdate();
		}
	}

	async function uploadFilesAndUpdate() {
		let pcImageFileName = null;
		let mobileImageFileName = null;

		try {
			// Upload PC image if selected
			const filePcImage = $('#filePcImage')[0].files[0];
			if (filePcImage !== undefined) {
				pcImageFileName = await uploadFile(filePcImage, '/info/promo/uploadPcImage', 'filePcImage');
			}

			// Upload Mobile image if selected
			const fileMobileImage = $('#fileMobileImage')[0].files[0];
			if (fileMobileImage !== undefined) {
				mobileImageFileName = await uploadFile(fileMobileImage, '/info/promo/uploadMobileImage', 'fileMobileImage');
			}

			// Now update the banner with JSON data
			const updateData = {
				id: $('#id').val(),
				title: $('#txtTitle').val(),
				infoDesc: CKEDITOR.instances.txtImgDesc.getData(),
				pcLinkUrl: "",
				mobileLinkUrl: "",
				linkMethod: "1",
				expsYn: $('input[name="radExpose"]:checked').val(),
				userId: $('#userId').val(),
				username: $('#username').val()
			};

			// Add file names if uploaded
			if (pcImageFileName) {
				updateData.filePcImageSvFileName = pcImageFileName;
			}
			if (mobileImageFileName) {
				updateData.fileMobileImageSvFileName = mobileImageFileName;
			}

			var jsonObj = {
				isShowLoading: true,
				method: 'POST',
				url: "/info/promo/updInfoPromoMain",
				contentType: 'application/json; charset=utf-8',
				cache: false,
				data: JSON.stringify(updateData),
				async: true,
				success: function(res) {
					alertMessage.show('#success-alert', 3000);
					setTimeout(() => location.reload(), 3000);
				},
				error: function(e1, e2, e3) {
					alertMessage.show('#error-alert', 3000);
				}
			};
			kidCommon.ajax(jsonObj);

		} catch (error) {
			console.error('Error uploading files:', error);
			alertMessage.show('#error-alert', 3000);
		}
	}

	function uploadFile(file, url, paramName) {
		return new Promise((resolve, reject) => {
			let formData = new FormData();
			formData.append(paramName, file);

			var jsonObj = {
				isShowLoading: true,
				method: 'POST',
				url: url,
				processData: false,
				contentType: false,
				cache: false,
				data: formData,
				enctype: 'multipart/form-data',
				async: true,
				success: function(res) {
					if (res.success && res.fileName) {
						resolve(res.fileName);
					} else {
						reject('Upload failed');
					}
				},
				error: function(e1, e2, e3) {
					reject('Upload error');
				}
			};
			kidCommon.ajax(jsonObj);
		});
	}
	function onSelectFileChange(element, imageContainerElement, name, initialWidth, intialHeight){
		$(element).on('click', function() {
			// Reset the file input value if the same file is selected again
			$(this).val('');
		});
		$(element).change(
				function() {
					let photoInput = $(element);
					let imgContainer = $(imageContainerElement)
					photoInput.removeData('imageWidth');
					imgContainer.hide().empty();
					var file = this.files[0];
					if (file && file.type.match(/image\/.*/)) {
						var reader = new FileReader();
						reader.onload = function () {
							const $img = $('<img />').attr({ src: reader.result });

							$img.on('load', function () {
								imgContainer.append($img).show();
								// Use intrinsic dimensions
								const naturalWidth = this.naturalWidth;
								const naturalHeight = this.naturalHeight;

								// Store intrinsic dimensions
								photoInput.data('imageWidth', naturalWidth);
								photoInput.data('imageHeight', naturalHeight);
								// Display the image in the container
								imgContainer.append($img).show();

								// Trigger validation
								validator.element(photoInput);
							});
						}

						$(this).attr("name", name);
						reader.readAsDataURL(file);
					} else {
						photoInput.addClass('error');
						$(this).attr("name", name);
						validator.element(photoInput);

					}
				});
	}
	function onDelete() {
		const params = {
			id : $('#id').val()
		}
		var jsonObj = {
			isShowLoading : true,
			method : 'POST',
			url : "/info/promo/delInfoPromo",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(res) {
				alertMessage.show('#success-alert', 3000);
				window.location = '/info/promo/getInfoPromoMain';
			},
			error : function(e1, e2, e3) {
				alertMessage.show('#error-alert', 3000);
			}
		}
		kidCommon.ajax(jsonObj);
	}

</script>
<div class="container-fluid p-0">
	<input type="hidden" value="#menuInfoPromoMain" id="screenName" />
	<input type="hidden" value="${banner.SEQ_NO}" id="id" />
	<input type="hidden" value="${sessionScope.userId}" id="userId" />
	<input type="hidden" value="${sessionScope.username}" id="username" />
	<h1 class="h3 mb-3">
		<strong>Promo</strong>
	</h1>
	<div class="row">
		<div class="col-12">
			<form id="formInfoPromoMain">
				<div class="card">
					<div class="card-body">
						<div class="row">
							<div class="col-12 col-lg-12">
								<label for="txtSubject" class="fw-bold">
									Title<span class="text-danger">*</span>
								</label>
								<input type="text" class="form-control" name="title" id="txtTitle" placeholder="" value="${banner.TITL}">
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="txtSubject" class="fw-bold">
									Show Y/N<span class="text-danger">*</span>
								</label>
								<div class="row mt-2">
									<div class="col-12 col-lg-12">
										<div class="input-readonly">
											<div class="col-12 col-lg-12">
												<div class="form-check form-check-inline">
													<input class="form-check-input" type="radio" name="radExpose" id="radExposeY" value="Y" ${banner.EXPS_YN == "Y" ? 'checked' : '' }>
													<label class="form-check-label" for="radExposeY">Yes</label>
												</div>
												<div class="form-check form-check-inline">
													<input class="form-check-input" type="radio" name="radExpose" id="radExposeN" value="N" ${banner.EXPS_YN == "N" ? 'checked' : '' }>
													<label class="form-check-label" for="radExposeN">No</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="uploadFile" class="fw-bold">
									PC Image<span class="text-danger">*</span>
								</label>
								<input type="file" class="form-control" id="filePcImage" />
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label class="fw-bold">
									<span class="text-danger">*</span> Only allow PNG file
								</label><br/>
								<label class="fw-bold">
									<span class="text-danger">*</span> Size: 996px x 360px
								</label>

							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<div id="filePcImageContainer">
									<img class="img-fluid" src="${pageContext.request.contextPath}/kid/upload/banner/${banner.IMG_URL}" />
									<br />
									<a href="${pageContext.request.contextPath}/kid/upload/banner/${banner.IMG_URL}">${banner.IMG_URL}</a>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="uploadFile" class="fw-bold">
									Mobile Image<span class="text-danger">*</span>
								</label>
								<input type="file" class="form-control" id="fileMobileImage" />
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label class="fw-bold">
									<span class="text-danger">*</span> Only allow PNG file
								</label><br/>
								<label class="fw-bold">
									<span class="text-danger">*</span> Size: 280px x 240px
								</label>

							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<div id="fileMobileImageContainer">
									<img class="img-fluid" src="${pageContext.request.contextPath}/kid/upload/banner/${banner.MOBILE_IMG_URL}" />
									<br />
									<a href="${pageContext.request.contextPath}/kid/upload/banner/${banner.MOBILE_IMG_URL}">${banner.MOBILE_IMG_URL}</a>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="txtSubject" class="fw-bold"> Description </label>
								<textarea class="form-control" style="height: 150px" name="txtImgDesc" id="txtImgDesc">${banner.INFO_DESC}</textarea>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="txtSubject" class="fw-bold"> Registration Date </label>
								<div class="input-readonly">
									<label>${banner.MAKEDATE}</label>
								</div>
							</div>
						</div>
						<div class="row mt-2">
							<div class="col-12 col-lg-12">
								<label for="txtSubject" class="fw-bold"> Modification Date </label>
								<div class="input-readonly">
									<label>${banner.UPDDATE}</label>
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="d-grid gap-2 d-md-flex justify-content-md-end">
								<a class="btn btn-light" href="/info/promo/getInfoPromoMain">List</a>
								<button type="button" class="btn btn-danger" id="btnDelete">Delete</button>
								<a class="btn btn-primary " id="btnSave">Save</a>
							</div>
						</div>
					</div>
				</div>
			</form>
			<div class="card">
				<div class="card-body">
					<div class="row">
						<div class="col-12 col-lg-6">
							<c:if test="${not empty bannerPrevious}">
								<a class="link-primary max-lines" href="/info/promo/updInfoPromoMain?id=${bannerPrevious.SEQ_NO}">
									<i class="align-middle" data-feather="chevrons-left"></i>
									<span class="align-middle">Previous: </span> <span class="align-middle">${bannerPrevious.MAKEDATE} - ${bannerPrevious.TITL}</span>
								</a>
							</c:if>
						</div>
						<div class="col-12 col-lg-6 d-flex justify-content-end">
							<c:if test="${not empty bannerNext}">
								<a class="link-primary max-lines-detail" href="/info/promo/updInfoPromoMain?id=${bannerNext.SEQ_NO}">
									<span class="align-middle">Next: </span> <span class="align-middle">${bannerNext.MAKEDATE} - ${bannerNext.TITL}</span>
									<i class="align-middle" data-feather="chevrons-right"></i>
								</a>
							</c:if>
						</div>
					</div>
				</div>
			</div>
			<div class="alert alert-success" id="success-alert" style="display: none;">
				<strong>Saved successfully.</strong>
			</div>
			<div class="alert alert-danger" role="alert" id="error-alert" style="display: none;">An error has occurred. Please contact your system administrator.</div>
		</div>
	</div>
</div>
<!-- Modal -->
<div class="modal fade" id="modalDelete" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header bg-danger bg-gradient">
				<h5 class="modal-title fw-bold text-light">Warning</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">Are you sure to delete?</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="btnDelConfirm">Confirm</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal -->
<div class="modal fade" id="modalConfirm" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header bg-primary bg-gradient">
				<h5 class="modal-title fw-bold text-light">Confirmation</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">Do you want to update as it is?</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="btnConfirm">Confirm</button>
			</div>
		</div>
	</div>
</div>
