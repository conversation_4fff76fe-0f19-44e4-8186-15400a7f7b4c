<?xml version="1.0" encoding="UTF-8"?>
<project name="local-kiwoom-indonesia-home" default="compile" basedir=".">

	<!-- Define project properties -->
	<property environment="env"/>
	<property name="build.workspace" value="${env.WORKSPACE}"/>
  	<property name="src.dir" value="${build.workspace}/src/main/java"/>
  	<property name="resource.dir" value="${build.workspace}/src/main/resources"/>
  	<property name="web.dir" value="${build.workspace}/WebContent"/>
	<property name="lib.dir" value="${web.dir}/WEB-INF/lib"/>
  	<property name="build.dir" value="${build.workspace}/build"/>
  	<property name="classes.dir" value="${build.dir}/classes"/>

  	<!-- Define classpath for compilation -->
  	<path id="compile.classpath">
    	<fileset dir="${lib.dir}">
      		<include name="*.jar"/>
    	</fileset>
  	</path>

	<!-- Find the java compiler -->
	<condition property="javac.executable" value="${java.home}/../bin/javac.exe" else="${java.home}/../bin/javac">
		<os family="windows" />
	</condition>
	<condition property="javac.executable" value="${java.home}/bin/javac.exe" else="${java.home}/bin/javac">
		<not>
			<available file="${javac.executable}" />
		</not>
		<os family="windows" />
	</condition>
	<fail message="Cannot find Java compiler. Make sure you are running Ant with a JDK.">
		<not><available file="${javac.executable}"/></not>
	</fail>
	
	<target name="information">
		<echo>* build.workspace   : ${build.workspace}</echo>
		<echo>* src.dir   : ${src.dir}</echo>
		<echo>* resource.dir   : ${resource.dir}</echo>
		<echo>* web.dir   : ${web.dir}</echo>
		<echo>* lib.dir   : ${lib.dir}</echo>
        <echo>* build.dir   : ${build.dir}</echo>
        <echo>* classes.dir : ${classes.dir}</echo>
		<echo>* copy classes.dir : ${build.dir}/WEB-INF/classes</echo>
    </target>
	
  	<!-- Define target for cleaning up build directory -->
  	<target name="clean">
    	<delete dir="${build.dir}"/>
  	</target>

  	<!-- Define target for compiling Java source code -->
  	<target name="compile">
    	<mkdir dir="${classes.dir}"/>
    	<javac srcdir="${src.dir}" destdir="${classes.dir}" debug="true" encoding="UTF-8">
      		<classpath refid="compile.classpath"/>
    	</javac>
    	<copy todir="${classes.dir}" overwrite="true">
      		<fileset dir="${resource.dir}"/>
    	</copy>
  	</target>
  	<!-- Copy classes and WEB-INF -->
  	<target name="copy-files">
    	<copy todir="${build.dir}" overwrite="true">
    		<fileset dir="${web.dir}">
    			<exclude name="WEB-INF/classes/**"/>
      		</fileset>
    	</copy>
    	<mkdir dir="${build.dir}/WEB-INF/classes"/>
    	<copy todir="${build.dir}/WEB-INF/classes" overwrite="true">
      		<fileset dir="${classes.dir}"/>
    	</copy>
    	<delete dir="${classes.dir}"/>
  	</target>
  	<!-- Define active profile -->
  	<target name="profile">
    	<copy file="${build.dir}/WEB-INF/classes/application-local.properties" tofile="${build.dir}/WEB-INF/classes/application.properties" overwrite="true"></copy>
    	<copy file="${build.dir}/WEB-INF/conf/properties/tcpc.properties.local" tofile="${build.dir}/WEB-INF/conf/properties/tcpc.properties" overwrite="true"></copy>
  	</target>
  	
	<target name="build_project" depends="information, clean, compile, copy-files, profile" />
</project>
