package id.co.kiwoom.kwis.info.promo.controller;

import id.co.kiwoom.kwis.banner.service.BannerService;
import id.co.kiwoom.kwis.banner.vo.BannerInVo;
import id.co.kiwoom.kwis.banner.vo.BannerRankInVo;
import id.co.kiwoom.kwis.banner.vo.BannerSearchVo;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Optional;

@Controller
@RequestMapping("/info/promo")
public class InfoPromoController {

	private static final String SVC_GB = "E02";
	private final BannerService bannerService;

	public InfoPromoController(BannerService bannerService) {
		this.bannerService = bannerService;
	}

	/** ? Get Info Event Main */
	@GetMapping("/getInfoPromoMain")
	public String getMarketMain(HttpServletRequest req) {
		return "frame:" + req.getContextPath() + "/info/promo/getInfoPromoMain";
	}

	@PostMapping(value = { "/getInfoPromoList" })
	@ResponseBody
	public Object getMarketList(BannerSearchVo params) throws Exception {
		return ResponseEntity.status(HttpStatus.OK).body(bannerService.getBannerList(params));
	}

	/** ? Register Info Event Main */
	@GetMapping("/regInfoPromoMain")
	public String regBanner(HttpServletRequest req) {
		return "frame:" + req.getContextPath() + "/info/promo/regInfoPromoMain";
	}

	/** ? Get Info Event Detail */
	@GetMapping("/detailInfoPromoMain")
	public String detailBanner(HttpServletRequest req, Model model, @RequestParam("id") int seqNo) {
		model.addAllAttributes(getBannerDetails(seqNo));
		return "frame:" + req.getContextPath() + "/info/promo/detailInfoPromoMain";
	}

	/** ? Update Info Event Main */
	@GetMapping("/updInfoPromoMain")
	public String updBanner(HttpServletRequest req, Model model, @RequestParam("id") int seqNo) {
		HashMap<String, Object> banners = getBannerDetails(seqNo);
		model.addAllAttributes(banners);

		int exposedCount = bannerService.countExposedBannerBySvcGb(SVC_GB, seqNo);
		model.addAttribute("exposedCount", exposedCount);

		return "frame:" + req.getContextPath() + "/info/promo/updInfoPromoMain";
	}

	@PostMapping(path = "/regInfoPromoMain")
	@ResponseBody
	public Object regBanner(BannerInVo bannerInVo) throws Exception {
		bannerInVo.setSvcGb(SVC_GB);
		return this.bannerService.insBanner(bannerInVo);
	}

	@PostMapping(path = "/uploadPcImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	@ResponseBody
	public Object uploadPcImage(@RequestParam("filePcImage") MultipartFile filePcImage) throws Exception {
		if (filePcImage == null || filePcImage.isEmpty()) {
			throw new IllegalArgumentException("PC image file is required");
		}

		BannerInVo bannerInVo = new BannerInVo();
		bannerInVo.setFilePcImage(filePcImage);
		bannerInVo.setSvcGb(SVC_GB);

		String fileName = this.bannerService.uploadPcImage(bannerInVo);

		HashMap<String, Object> response = new HashMap<>();
		response.put("fileName", fileName);
		response.put("success", true);

		return ResponseEntity.status(HttpStatus.OK).body(response);
	}

	@PostMapping(path = "/uploadMobileImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	@ResponseBody
	public Object uploadMobileImage(@RequestParam("fileMobileImage") MultipartFile fileMobileImage) throws Exception {
		if (fileMobileImage == null || fileMobileImage.isEmpty()) {
			throw new IllegalArgumentException("Mobile image file is required");
		}

		BannerInVo bannerInVo = new BannerInVo();
		bannerInVo.setFileMobileImage(fileMobileImage);
		bannerInVo.setSvcGb(SVC_GB);

		String fileName = this.bannerService.uploadMobileImage(bannerInVo);

		HashMap<String, Object> response = new HashMap<>();
		response.put("fileName", fileName);
		response.put("success", true);

		return ResponseEntity.status(HttpStatus.OK).body(response);
	}

	@PostMapping(path = "/updInfoPromoMain", consumes = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public Object updBanner(@RequestBody BannerInVo bannerInVo) throws Exception {
		bannerInVo.setSvcGb(SVC_GB);
		bannerInVo.setInfoDesc(decodeText(bannerInVo.getInfoDesc()));
		bannerInVo.setTitle(decodeText(bannerInVo.getTitle()));
		return this.bannerService.updBannerWithoutFile(bannerInVo);
	}

	@PostMapping(path = "/delInfoPromo")
	@ResponseBody
	public Object delBanner(@RequestBody BannerInVo bannerInVo) throws Exception {
		bannerInVo.setSvcGb(SVC_GB);
		bannerInVo.setInfoDesc(decodeText(bannerInVo.getInfoDesc()));
		bannerInVo.setTitle(decodeText(bannerInVo.getTitle()));
		return this.bannerService.delBanner(bannerInVo);
	}

	@PostMapping(path = "/updInfoPromoRank")
	@ResponseBody
	public Object updBannerRank(@RequestBody BannerRankInVo bannerRankInVo) throws Exception {
		this.bannerService.updBannerRank(bannerRankInVo);
		return true;
	}

	// ================================================================================
	// HELPER METHODS
	// ================================================================================

	private HashMap<String, Object> getBannerDetails(int seqNo) {
		return bannerService.selBannerDetail(seqNo, SVC_GB);
	}

	private String decodeText(String text) {
		return Optional.ofNullable(text)
				.map(t -> URLDecoder.decode(t, StandardCharsets.UTF_8))
				.orElse(null);
	}
}
