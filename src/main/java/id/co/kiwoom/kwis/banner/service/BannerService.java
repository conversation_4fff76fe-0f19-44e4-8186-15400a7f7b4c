package id.co.kiwoom.kwis.banner.service;

import id.co.kiwoom.kwis.banner.mapper.BannerMapper;
import id.co.kiwoom.kwis.banner.vo.*;
import id.co.kiwoom.kwis.common.service.FileService;
import id.co.kiwoom.kwis.common.vo.PaginationOutVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class BannerService {

	private final BannerMapper bannerMapper;
	private final FileService fileService;

	@Value("${home.web.banner.image.dir}")
	String homeBannerImgDir = "";

	@Value("${home.main.banner.json.filename}")
	String mainBannerName = "";

	@Value("${home.middle.banner.json.filename}")
	String middleBannerName = "";

	@Value("${home.front.banner.json.filename}")
	String frontBannerName = "";

	@Value("${home.front.bottom.banner.json.filename}")
	String frontBottomBannerName = "";

	@Value("${home.mts.home.banner.json.filename}")
	String mtsHomeBannerName = "";

	@Value("${home.mts.menubar.banner.json.filename}")
	String mtsMenuBarName = "";

	@Value("${home.mts.totalasset.banner.json.filename}")
	String mtsTotalAssetName = "";

	public BannerService(BannerMapper bannerMapper, FileService fileService) {
		this.bannerMapper = bannerMapper;
		this.fileService = fileService;
	}

	public PaginationOutVo getBannerList(BannerSearchVo params) throws Exception {
		List<HashMap<String, Object>> Banner = this.bannerMapper.selBannerList(params);
		int totalCount = this.bannerMapper.selBannerTotalCnt(params);
		return new PaginationOutVo(Banner, totalCount);
	}

	public int countExposedBannerBySvcGb(String svcGb, int seqNo){
		return this.bannerMapper.countExposedBannerBySvcGb(svcGb, seqNo);
	}

	public boolean insBanner(BannerInVo params) throws Exception {
		saveBannerFile(params);
		int rank = this.bannerMapper.selNextRank(params.getSvcGb());
		params.setRank(rank);
		this.bannerMapper.insBanner(params);
		saveJsonFile(params.getSvcGb(), "Y");
		return true;
	}

	public boolean updBanner(BannerInVo params) throws Exception {
		saveBannerFile(params);
		this.bannerMapper.updBanner(params);
		saveJsonFile(params.getSvcGb(), "Y");
		return true;
	}

	public boolean updBannerWithoutFile(BannerInVo params) throws Exception {
		this.bannerMapper.updBanner(params);
		saveJsonFile(params.getSvcGb(), "Y");
		return true;
	}

	public String uploadPcImage(BannerInVo params) throws Exception {
		StringBuilder basePath = new StringBuilder(fileService.getBasePath("7"));
		if (params.getFilePcImage() != null) {
			String serverFileName = fileService.saveFile(params.getFilePcImage(), basePath.toString(),
					Objects.requireNonNull(params.getFilePcImage().getOriginalFilename()).replace(" ", "_"));
			params.setFilePcImageName(params.getFilePcImage().getOriginalFilename());
			params.setFilePcImageSvFileName(serverFileName);
			return serverFileName;
		}
		return null;
	}

	public String uploadMobileImage(BannerInVo params) throws Exception {
		StringBuilder basePath = new StringBuilder(fileService.getBasePath("7"));
		if (params.getFileMobileImage() != null) {
			String serverFileName = fileService.saveFile(params.getFileMobileImage(), basePath.toString(),
					Objects.requireNonNull(params.getFileMobileImage().getOriginalFilename()).replace(" ", "_"));
			params.setFileMobileImageName(params.getFileMobileImage().getOriginalFilename());
			params.setFileMobileImageSvFileName(serverFileName);
			return serverFileName;
		}
		return null;
	}

	public boolean delBanner(BannerInVo params) throws Exception {
		this.bannerMapper.delBanner(params);
		saveJsonFile(params.getSvcGb(), "Y");
		return true;
	}

	public HashMap<String, Object> getBannerDetail(int seqNo, String svcGb) {
		return selBannerDetail(seqNo, svcGb);
	}

	public void updBannerRank(BannerRankInVo params) throws Exception {
		for (BannerRankVo rank : params.getRanks()) {
			this.bannerMapper.updBannerRank(rank);
		}
		saveJsonFile(params.getSvcGb(), "Y");
	}

	public HashMap<String, Object> selBannerDetail(int seqNo, String svcGb) {
		HashMap<String, Object> banners = new HashMap<>();
		List<HashMap<String, Object>> bannerList = this.bannerMapper.selBannerDetail(seqNo, svcGb);
		banners.put("banner", bannerList.stream().filter(x -> "1".equals(x.get("GUBUN"))).findAny().orElse(null));
		banners.put("bannerPrevious",
				bannerList.stream().filter(x -> "2".equals(x.get("GUBUN"))).findAny().orElse(null));
		banners.put("bannerNext", bannerList.stream().filter(x -> "3".equals(x.get("GUBUN"))).findAny().orElse(null));
		return banners;
	}

	private void saveBannerFile(BannerInVo bannerInVo) throws Exception {
		StringBuilder basePath = new StringBuilder(fileService.getBasePath("7"));
		if (bannerInVo.getFileMobileImage() != null) {
			String serverFileName = fileService.saveFile(bannerInVo.getFileMobileImage(), basePath.toString(),
					Objects.requireNonNull(bannerInVo.getFileMobileImage().getOriginalFilename()).replace(" ", "_"));
			bannerInVo.setFileMobileImageName(bannerInVo.getFileMobileImage().getOriginalFilename());
			bannerInVo.setFileMobileImageSvFileName(serverFileName);
		}
		if (bannerInVo.getFilePcImage() != null) {
			String serverFileName = fileService.saveFile(bannerInVo.getFilePcImage(), basePath.toString(),
					Objects.requireNonNull(bannerInVo.getFilePcImage().getOriginalFilename()).replace(" ", "_"));
			bannerInVo.setFilePcImageName(bannerInVo.getFilePcImage().getOriginalFilename());
			bannerInVo.setFilePcImageSvFileName(serverFileName);
		}
	}

	private void saveJsonFile(String svcGb, String expsYn) throws Exception {
		if(Objects.equals(svcGb, "E01") || Objects.equals(svcGb, "E02")) {
			return;
		}

		if (Objects.equals(expsYn, "Y")) {
			BannerUploadVo bannerUpload = new BannerUploadVo(svcGb, expsYn);
			Object finalData = null;
			List<BannerJsonVo> banners = this.bannerMapper.selBannerByCondition(bannerUpload);
			String bannerName = "";
			switch (svcGb) {
				case "H01":
					bannerName = mainBannerName;
					finalData = banners;
					break;
				case "H02":
					bannerName = middleBannerName;
					finalData = banners;
					break;
				case "F01":
					bannerName = frontBannerName;
					finalData = banners.stream().map(banner -> new BannerFrontJsonVo(banner.getSeqNo(),banner.getRank(),banner.getImgFileUrl(), banner.getAlt(),banner.getPcLinkUrl(),banner.getLinkMethod())).collect(Collectors.toList());
					break;
				case "F02":
					bannerName = frontBottomBannerName;
					finalData = banners.stream().map(banner -> new BannerFrontJsonVo(banner.getSeqNo(),banner.getRank(),banner.getImgFileUrl(),banner.getAlt(),banner.getPcLinkUrl(),banner.getLinkMethod())).collect(Collectors.toList());
					break;
				case "M01":
					bannerName = mtsHomeBannerName;
					finalData = banners.stream().map(banner -> new BannerMtsHomeJsonVo(banner.getSeqNo(),banner.getRank(),homeBannerImgDir + banner.getImgFileUrl(),banner.getMobileLinkUrl())).collect(Collectors.toList());
					break;
				case "M02":
					bannerName = mtsMenuBarName;
					finalData = banners.stream().map(banner -> new BannerMtsHomeJsonVo(banner.getSeqNo(),banner.getRank(),homeBannerImgDir + banner.getImgFileUrl(),banner.getMobileLinkUrl())).collect(Collectors.toList());
					break;
				case "M03":
					bannerName = mtsTotalAssetName;
					finalData = banners.stream().map(banner -> new BannerMtsHomeJsonVo(banner.getSeqNo(),banner.getRank(),homeBannerImgDir + banner.getImgFileUrl(),banner.getMobileLinkUrl())).collect(Collectors.toList());
					break;
				default:
					break;
			}

			if (!bannerName.isEmpty()) {
				String jsonData = new com.google.gson.Gson().toJson(finalData);
				fileService.writeJsonFile(jsonData, fileService.getBasePath("10"), bannerName);
			}
		}
	}

	// For backward compatibility
	public List<HashMap<String, Object>> getBanners(BannerSearchVo params) {
		return this.bannerMapper.selBannerList(params);
	}
}
