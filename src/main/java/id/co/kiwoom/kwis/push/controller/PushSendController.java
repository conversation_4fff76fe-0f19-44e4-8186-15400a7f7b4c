package id.co.kiwoom.kwis.push.controller;

import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import id.co.kiwoom.kwis.common.exception.BusinessException;
import id.co.kiwoom.kwis.common.util.DateUtil;
import id.co.kiwoom.kwis.push.service.PushSendService;
import id.co.kiwoom.kwis.push.vo.PushFileDataVo;
import id.co.kiwoom.kwis.push.vo.PushSendSearchVo;
import id.co.kiwoom.kwis.push.vo.PushSendVo;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Controller
@RequestMapping("/push/pushsend")
@RequiredArgsConstructor
public class PushSendController {
	
	private final PushSendService pushSendService;
	
	@GetMapping(value = { "/getPushSendMain" })
	public Object getPushSendMain(HttpServletRequest req, HttpServletResponse res) throws Exception {
		return "frame:" + req.getContextPath() + "/push/pushsend/getPushSendMain";
	}
	
	@PostMapping(value = "/getPushSendList")
	@ResponseBody
	public Object getPushSendList(PushSendSearchVo params) throws Exception {
		return ResponseEntity.status(HttpStatus.OK).body(pushSendService.getPushSendList(params));
	}
	
	@PostMapping(value = "/getMarketingAgreementUsers")
	@ResponseBody
	public Object getMarketingAgreementUsers(@RequestBody PushSendVo params) throws Exception {
		return ResponseEntity.status(HttpStatus.OK).body(pushSendService.getMarketingAgreementUsers (params));
	}
	
	@GetMapping(value = "/regPushSendMain")
	public Object regPushSendMain(HttpServletRequest request, HttpServletResponse response, Model model) throws Exception {
		String today = DateUtil.getCurrentDate("yyyy/MM/dd");
		String curDate = DateUtil.getCurrentTime("yyyyMMddHHmmss");
		
		if(!"".equals(curDate)) {
			String curHour = curDate.substring(8,10);
			String curMinute = curDate.substring(10,12);
			String setHour = "";
			String setMinute = "";
			
			if(Integer.parseInt(curHour) < 9) {
				setHour = "09";
				setMinute = "00";
			}else if(Integer.parseInt(curHour) >= 18) {	
				setHour = "18";
				setMinute = "00";
			}else {
				setHour = curHour;
				if(Integer.parseInt(curMinute) >= 0 && Integer.parseInt(curMinute) < 10)	setMinute = "10";
				if(Integer.parseInt(curMinute) >= 10 && Integer.parseInt(curMinute) < 20)	setMinute = "20";
				if(Integer.parseInt(curMinute) >= 20 && Integer.parseInt(curMinute) < 30)	setMinute = "30";
				if(Integer.parseInt(curMinute) >= 30 && Integer.parseInt(curMinute) < 40)	setMinute = "40";
				if(Integer.parseInt(curMinute) >= 40 && Integer.parseInt(curMinute) < 50)	setMinute = "50";
				if(Integer.parseInt(curMinute) >= 50) {
					setHour = Integer.toString(Integer.parseInt(setHour) + 1);
					setMinute = "00";
				}
			}
			
			model.addAttribute("setHour", setHour);
			model.addAttribute("setMinute", setMinute);
		}

		model.addAttribute("today", today);
		
		
		return "frame:" + request.getContextPath() + "/push/pushsend/regPushSendMain";
	}
	
	@PostMapping(value = "/regPushFile")
	@ResponseBody
	public Object regPushFile(@RequestParam("uploadFile") MultipartFile file) throws TikaException, IOException {
		
		List<PushFileDataVo> dataList = new ArrayList<>();
        try (InputStream is = file.getInputStream();) {
            Tika tika = new Tika();
            String mimeType = tika.detect(is);
            if (isAllowedMIMEType(mimeType)) {
                Workbook workbook = new XSSFWorkbook(file.getInputStream());
                Sheet worksheet = workbook.getSheetAt(0);
                
                for (int i = 0; i < worksheet.getPhysicalNumberOfRows(); i++) {
                    Row row = worksheet.getRow(i);
                    
                    PushFileDataVo data = new PushFileDataVo();
                    data.setUserId(row.getCell(0).getStringCellValue());

                    dataList.add(data);
                }
                
            } else {
            	throw new IOException();
            }
            
        } catch (FileNotFoundException e) {
        	throw new BusinessException("Failed to save file upload.");
        } catch (Exception e) {
        	throw new BusinessException("Failed to save file upload.");
        }
        
		
        return dataList;
	}
	
	@PostMapping(value = "/regTestPushSend")
	@ResponseBody
	public Object regTestPushSend(@Valid PushSendVo params) throws Exception{
		String subject = StringEscapeUtils.unescapeHtml4(URLDecoder.decode(params.getPushSubject(), "UTF-8"));
		String fullMessage = StringEscapeUtils.unescapeHtml4(URLDecoder.decode(params.getPushFullMsg(), "UTF-8"));
		
		params.setPushSubject(subject);
		params.setPushFullMsg(fullMessage);
		
		boolean result = this.pushSendService.insertPushTestSend(params);
		if(!result)	throw new BusinessException("Failed to send push test.");
		
		return result;
	}
	
	@PostMapping(value = "/regPushSend")
	@ResponseBody
	public Object regPushSend(@Valid PushSendVo params) throws Exception{
		
		String hourMinute = params.getHourMinute();
		String sendTime = "";
		String today = DateUtil.getCurrentDate("yyyyMMdd");
		
		String cpnSubject = StringEscapeUtils.unescapeHtml4(URLDecoder.decode(params.getPushCpnSubject(), "UTF-8"));
		String subject = StringEscapeUtils.unescapeHtml4(URLDecoder.decode(params.getPushSubject(), "UTF-8"));
		String fullMessage = StringEscapeUtils.unescapeHtml4(URLDecoder.decode(params.getPushFullMsg(), "UTF-8"));
		
		params.setPushCpnSubject(cpnSubject);
		params.setPushSubject(subject);
		params.setPushFullMsg(fullMessage);
		
		String currentHourMinute = Integer.toString(LocalDateTime.now().getHour()) + Integer.toString(LocalDateTime.now().getMinute());
		if (Integer.parseInt(hourMinute) < Integer.parseInt(currentHourMinute)) {
			throw new BusinessException("Cannot enter a time ealier than the current time.");
		}
		
		if(!"".equals(hourMinute)) {
			sendTime = today + hourMinute + "00";
			params.setSendTime(DateUtil.stringToDate(sendTime));
		}
		
		boolean result = this.pushSendService.insertPushSend(params);
		if(!result)	throw new BusinessException("This campaign failed to save.");
		
		return result;
	}
	
	@GetMapping(value = { "/detailPushSendMain" })
	public Object detailPushSendMain(HttpServletRequest req, HttpServletResponse res, Model model, @RequestParam("pushCpnSeq") int pushCpnSeq) throws Exception {
		HashMap<String, Object> detail = pushSendService.getPushSendDetail(pushCpnSeq);
		
		model.addAttribute("detail", detail);
		return "frame:" + req.getContextPath() + "/push/pushsend/detailPushSendMain";
	}
	
	@PostMapping(value = "/getPushReservedTargetUsers")
	@ResponseBody
	public Object getPushReservedTargetUsers(@RequestBody PushSendVo params) throws Exception {
		return ResponseEntity.status(HttpStatus.OK).body(pushSendService.getPushReservedTargetUsers (params));
	}
	
	@PostMapping(value = "/delPushSend")
	@ResponseBody
	public Object delPushSend(@RequestBody PushSendVo params) throws Exception {
		boolean result = this.pushSendService.deletePushSend(params);
		if(!result)	throw new BusinessException("This campaign failed to delete.");
		
		return result;
	}
	
	private boolean isAllowedMIMEType(String mimeType) {
        if (mimeType.equals("application/x-tika-ooxml"))	return true;
        return false;
    }
}
